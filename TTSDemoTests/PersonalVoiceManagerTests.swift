import XCTest
import AVFoundation
@testable import TTSDemo

@MainActor
final class PersonalVoiceManagerTests: XCTestCase {
    var personalVoiceManager: PersonalVoiceManager!
    
    override func setUp() {
        super.setUp()
        personalVoiceManager = PersonalVoiceManager()
    }
    
    override func tearDown() {
        personalVoiceManager = nil
        super.tearDown()
    }
    
    func testInitialState() {
        XCTAssertEqual(personalVoiceManager.enrollmentProgress, 0.0)
        XCTAssertFalse(personalVoiceManager.isEnrolling)
    }
    
    func testPersonalVoiceSupport() {
        if #available(iOS 17.0, *) {
            XCTAssertTrue(personalVoiceManager.isPersonalVoiceSupported)
        } else {
            XCTAssertFalse(personalVoiceManager.isPersonalVoiceSupported)
        }
    }
    
    func testAuthorizationStatusDescription() {
        let descriptions = [
            AVSpeechSynthesizer.PersonalVoiceAuthorizationStatus.notDetermined: "未确定",
            .denied: "已拒绝",
            .unsupported: "不支持",
            .authorized: "已授权"
        ]
        
        for (status, expectedDescription) in descriptions {
            personalVoiceManager.personalVoiceAuthorizationStatus = status
            XCTAssertEqual(personalVoiceManager.authorizationStatusDescription, expectedDescription)
        }
    }
    
    func testPersonalVoicesRetrieval() {
        let personalVoices = personalVoiceManager.getPersonalVoices()
        // Personal voices may or may not be available depending on device and setup
        XCTAssertTrue(personalVoices.allSatisfy { $0.voiceTraits.contains(.isPersonalVoice) })
    }
    
    func testHasPersonalVoices() {
        let hasVoices = personalVoiceManager.hasPersonalVoices
        let actualVoices = personalVoiceManager.getPersonalVoices()
        XCTAssertEqual(hasVoices, !actualVoices.isEmpty)
    }
    
    func testCanEnrollPersonalVoice() {
        // Test when not supported
        if !personalVoiceManager.isPersonalVoiceSupported {
            XCTAssertFalse(personalVoiceManager.canEnrollPersonalVoice)
            return
        }
        
        // Test when not authorized
        personalVoiceManager.personalVoiceAuthorizationStatus = .denied
        XCTAssertFalse(personalVoiceManager.canEnrollPersonalVoice)
        
        // Test when enrolling
        personalVoiceManager.personalVoiceAuthorizationStatus = .authorized
        personalVoiceManager.isEnrolling = true
        XCTAssertFalse(personalVoiceManager.canEnrollPersonalVoice)
        
        // Test when can enroll
        personalVoiceManager.isEnrolling = false
        XCTAssertTrue(personalVoiceManager.canEnrollPersonalVoice)
    }
    
    func testErrorHandling() {
        let testError = NSError(domain: "TestDomain", code: 1, userInfo: [NSLocalizedDescriptionKey: "Test error"])
        
        personalVoiceManager.handleEnrollmentError(testError)
        
        XCTAssertFalse(personalVoiceManager.isEnrolling)
        XCTAssertEqual(personalVoiceManager.enrollmentProgress, 0.0)
        XCTAssertNotNil(personalVoiceManager.enrollmentError)
    }
    
    func testErrorClearing() {
        personalVoiceManager.enrollmentError = "Test error"
        XCTAssertNotNil(personalVoiceManager.enrollmentError)
        
        personalVoiceManager.clearEnrollmentError()
        XCTAssertNil(personalVoiceManager.enrollmentError)
    }
}
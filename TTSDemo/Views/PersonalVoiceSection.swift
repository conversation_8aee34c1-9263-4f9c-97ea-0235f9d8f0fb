import SwiftUI
import AVFoundation

struct PersonalVoiceSection: View {
    @Bindable var personalVoiceManager: PersonalVoiceManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("个人语音")
                .font(.headline)
                .foregroundColor(.primary)
            
            if !personalVoiceManager.isPersonalVoiceSupported {
                // Device not supported
                UnsupportedDeviceView()
            } else {
                VStack(spacing: 16) {
                    // Authorization Status
                    AuthorizationStatusView(
                        status: personalVoiceManager.personalVoiceAuthorizationStatus,
                        statusDescription: personalVoiceManager.authorizationStatusDescription
                    )
                    
                    // Action Buttons
                    ActionButtonsView(
                        personalVoiceManager: personalVoiceManager
                    )
                    
                    // Enrollment Progress
                    if personalVoiceManager.isEnrolling {
                        EnrollmentProgressView(
                            progress: personalVoiceManager.enrollmentProgress,
                            personalVoiceManager: personalVoiceManager
                        )
                    }
                    
                    // Personal Voices List
                    if personalVoiceManager.hasPersonalVoices {
                        PersonalVoicesListView(
                            voices: personalVoiceManager.getPersonalVoices()
                        )
                    }
                }
            }
        }
        .padding(.vertical, 8)
    }
}

struct UnsupportedDeviceView: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle")
                .font(.title2)
                .foregroundColor(.orange)
            
            Text("设备不支持个人语音功能")
                .font(.body)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
            
            Text("个人语音功能需要 iOS 17 或更高版本")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct AuthorizationStatusView: View {
    let status: AVSpeechSynthesizer.PersonalVoiceAuthorizationStatus
    let statusDescription: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: statusIcon)
                .font(.title3)
                .foregroundColor(statusColor)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("授权状态")
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(statusDescription)
                    .font(.caption)
                    .foregroundColor(statusColor)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var statusIcon: String {
        switch status {
        case .notDetermined:
            return "questionmark.circle"
        case .denied:
            return "xmark.circle"
        case .unsupported:
            return "exclamationmark.triangle"
        case .authorized:
            return "checkmark.circle"
        @unknown default:
            return "questionmark.circle"
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .notDetermined:
            return .orange
        case .denied:
            return .red
        case .unsupported:
            return .orange
        case .authorized:
            return .green
        @unknown default:
            return .gray
        }
    }
}

struct ActionButtonsView: View {
    @Bindable var personalVoiceManager: PersonalVoiceManager
    
    var body: some View {
        VStack(spacing: 12) {
            if personalVoiceManager.personalVoiceAuthorizationStatus == .notDetermined {
                VStack(spacing: 8) {
                    Text("需要录音权限来创建个人语音")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Button(action: {
                        Task {
                            await personalVoiceManager.requestPersonalVoiceAuthorization()
                        }
                    }) {
                        HStack {
                            Image(systemName: "mic.badge.key")
                            Text("申请录音权限")
                        }
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue)
                        .cornerRadius(12)
                    }
                }
            } else if personalVoiceManager.personalVoiceAuthorizationStatus == .denied {
                VStack(spacing: 8) {
                    Text("录音权限被拒绝，请在设置中开启")
                        .font(.caption)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                    
                    Button(action: {
                        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(settingsUrl)
                        }
                    }) {
                        HStack {
                            Image(systemName: "gear")
                            Text("前往设置开启权限")
                        }
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.orange)
                        .cornerRadius(12)
                    }
                }
            } else if personalVoiceManager.personalVoiceAuthorizationStatus == .authorized {
                if personalVoiceManager.hasPersonalVoices {
                    VStack(spacing: 8) {
                        Text("个人语音已就绪，可以在语音选择中使用")
                            .font(.caption)
                            .foregroundColor(.green)
                            .multilineTextAlignment(.center)
                        
                        Button(action: {
                            Task {
                                await personalVoiceManager.startPersonalVoiceEnrollment()
                            }
                        }) {
                            HStack {
                                Image(systemName: "mic.badge.plus")
                                Text("注册新的个人语音")
                            }
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.purple)
                            .cornerRadius(12)
                        }
                        .disabled(personalVoiceManager.isEnrolling)
                    }
                } else if personalVoiceManager.canEnrollPersonalVoice {
                    VStack(spacing: 8) {
                        Text("权限已获得，现在可以开始录音创建个人语音")
                            .font(.caption)
                            .foregroundColor(.green)
                            .multilineTextAlignment(.center)
                        
                        Button(action: {
                            Task {
                                await personalVoiceManager.startPersonalVoiceEnrollment()
                            }
                        }) {
                            HStack {
                                Image(systemName: "mic.circle.fill")
                                Text("开始录音创建个人语音")
                            }
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.purple)
                            .cornerRadius(12)
                        }
                        .disabled(personalVoiceManager.isEnrolling)
                    }
                }
            }
        }
    }
}

struct EnrollmentProgressView: View {
    let progress: Float
    @Bindable var personalVoiceManager: PersonalVoiceManager
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("等待个人语音注册")
                    .font(.body)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.body)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                .scaleEffect(y: 2)
            
            VStack(spacing: 8) {
                Text("请在系统设置中完成个人语音注册：")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Text("设置 → 辅助功能 → 个人语音")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.purple)
                    .multilineTextAlignment(.center)
                
                Text("注册完成后，个人语音将自动出现在语音选择列表中")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: {
                personalVoiceManager.cancelEnrollment()
            }) {
                Text("取消等待")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct PersonalVoicesListView: View {
    let voices: [AVSpeechSynthesisVoice]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("已注册的个人语音")
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            ForEach(voices, id: \.identifier) { voice in
                HStack(spacing: 12) {
                    Image(systemName: "person.fill")
                        .foregroundColor(.purple)
                        .font(.body)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(voice.name)
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text(voice.language)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                }
                .padding()
                .background(Color.purple.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    PersonalVoiceSection(personalVoiceManager: PersonalVoiceManager())
        .padding()
}
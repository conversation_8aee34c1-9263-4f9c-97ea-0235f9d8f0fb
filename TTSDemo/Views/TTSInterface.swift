import SwiftUI

struct TTSInterface: View {
    @State private var ttsManager = TTSManager()
    @State private var personalVoiceManager = PersonalVoiceManager()
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Text Input Section
                    TextInputSection(text: $ttsManager.currentText)
                    
                    Divider()
                    
                    // Playback Control Section
                    PlaybackControlSection(
                        isPlaying: ttsManager.isPlaying,
                        canPlay: !ttsManager.currentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
                        onPlayTapped: {
                            ttsManager.speakText(ttsManager.currentText)
                        },
                        onStopTapped: {
                            ttsManager.stopSpeaking()
                        }
                    )
                    
                    Divider()
                    
                    // Voice Selection Section
                    VoiceSelectionSection(
                        voices: ttsManager.availableVoices,
                        selectedVoice: $ttsManager.selectedVoice,
                        personalVoices: ttsManager.personalVoices,
                        onVoiceSelected: { voice in
                            ttsManager.selectVoice(voice)
                        }
                    )
                    
                    Divider()
                    
                    // Parameter Control Section
                    ParameterControlSection(
                        speechRate: $ttsManager.speechRate,
                        speechVolume: $ttsManager.speechVolume,
                        speechPitch: $ttsManager.speechPitch,
                        onRateChanged: { rate in
                            ttsManager.updateSpeechRate(rate)
                        },
                        onVolumeChanged: { volume in
                            ttsManager.updateSpeechVolume(volume)
                        },
                        onPitchChanged: { pitch in
                            ttsManager.updateSpeechPitch(pitch)
                        }
                    )
                    
                    Divider()
                    
                    // Personal Voice Section
                    PersonalVoiceSection(personalVoiceManager: personalVoiceManager)
                    
                    // Error Message Display
                    if let errorMessage = ttsManager.errorMessage {
                        ErrorMessageView(message: errorMessage) {
                            ttsManager.errorMessage = nil
                        }
                    }
                    
                    // Personal Voice Error Display
                    if let enrollmentError = personalVoiceManager.enrollmentError {
                        ErrorMessageView(message: enrollmentError) {
                            personalVoiceManager.clearEnrollmentError()
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("文本转语音")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                // Refresh personal voices when view appears
                Task {
                    await ttsManager.loadAvailableVoices()
                }
                
                // Check personal voice availability
                personalVoiceManager.checkPersonalVoiceAvailability()
                
                // Announce screen change for VoiceOver users
                TTSAccessibilityAnnouncer.shared.announceScreenChange()
            }
            .onChange(of: personalVoiceManager.personalVoiceAuthorizationStatus) { _, _ in
                // Refresh voices when personal voice authorization changes
                Task {
                    await ttsManager.loadAvailableVoices()
                }
            }
            .onChange(of: ttsManager.isPlaying) { _, isPlaying in
                // Announce playback state changes
                let message = isPlaying ? "开始播放语音" : "停止播放语音"
                TTSAccessibilityAnnouncer.shared.announce(message)
            }
            .onChange(of: ttsManager.errorMessage) { _, errorMessage in
                // Announce errors to VoiceOver users
                if let error = errorMessage {
                    TTSAccessibilityAnnouncer.shared.announce("错误：\(error)")
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
}

struct ErrorMessageView: View {
    let message: String
    let onDismiss: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
                .font(.title3)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("错误")
                    .font(.headline)
                    .foregroundColor(.red)
                
                Text(message)
                    .font(.body)
                    .foregroundColor(.primary)
            }
            
            Spacer()
            
            Button(action: onDismiss) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.gray)
                    .font(.title3)
            }
        }
        .padding()
        .background(Color.red.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("错误消息")
        .accessibilityValue(message)
        .accessibilityHint("点击关闭按钮以关闭此错误消息")
    }
}

#Preview {
    TTSInterface()
}

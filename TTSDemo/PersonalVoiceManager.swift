import Foundation
import UIKit
import AVFoundation
import Observation

@MainActor
@Observable
class PersonalVoiceManager {
    var isPersonalVoiceAvailable: Bool = false
    var personalVoiceAuthorizationStatus: AVSpeechSynthesizer.PersonalVoiceAuthorizationStatus = .notDetermined
    var enrollmentProgress: Float = 0.0
    var isEnrolling: Bool = false
    
    init() {
        // 初始化时不立即检查，让用户主动触发权限申请
        if #available(iOS 17.0, *) {
            personalVoiceAuthorizationStatus = AVSpeechSynthesizer.personalVoiceAuthorizationStatus
        } else {
            personalVoiceAuthorizationStatus = .unsupported
        }
    }
    
    // MARK: - Personal Voice Authorization
    func requestPersonalVoiceAuthorization() async {
        let status = await AVSpeechSynthesizer.requestPersonalVoiceAuthorization()
        await MainActor.run {
            self.personalVoiceAuthorizationStatus = status
            self.checkPersonalVoiceAvailability()
        }
    }
    
    func checkPersonalVoiceAvailability() {
        if #available(iOS 17.0, *) {
            personalVoiceAuthorizationStatus = AVSpeechSynthesizer.personalVoiceAuthorizationStatus
            isPersonalVoiceAvailable = personalVoiceAuthorizationStatus == .authorized
        } else {
            personalVoiceAuthorizationStatus = .unsupported
            isPersonalVoiceAvailable = false
        }
    }
    
    // MARK: - Device Support Check
    var isPersonalVoiceSupported: Bool {
        if #available(iOS 17.0, *) {
            return true
        } else {
            return false
        }
    }
    
    var authorizationStatusDescription: String {
        switch personalVoiceAuthorizationStatus {
        case .notDetermined:
            return "需要申请录音权限"
        case .denied:
            return "录音权限被拒绝"
        case .unsupported:
            return "设备不支持个人语音"
        case .authorized:
            return "录音权限已获得"
        @unknown default:
            return "未知状态"
        }
    }
    
    // MARK: - Personal Voice Enrollment
    func startPersonalVoiceEnrollment() async {
        guard personalVoiceAuthorizationStatus == .authorized else {
            enrollmentError = TTSError.personalVoiceNotAuthorized.localizedDescription
            return
        }
        
        await MainActor.run {
            isEnrolling = true
            enrollmentProgress = 0.0
            enrollmentError = nil
        }
        
        // 引导用户到系统设置进行个人语音注册
        await MainActor.run {
            openPersonalVoiceSettings()
        }
        
        // 开始监控个人语音注册状态
        await monitorEnrollmentProgress()
    }
    
    private func openPersonalVoiceSettings() {
        // 尝试打开个人语音设置页面
        if let settingsUrl = URL(string: "App-prefs:ACCESSIBILITY&path=PERSONAL_VOICE") {
            UIApplication.shared.open(settingsUrl) { success in
                if !success {
                    // 如果无法直接打开个人语音设置，打开辅助功能设置
                    if let accessibilityUrl = URL(string: "App-prefs:ACCESSIBILITY") {
                        UIApplication.shared.open(accessibilityUrl)
                    } else {
                        // 最后回退到通用设置
                        if let generalUrl = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(generalUrl)
                        }
                    }
                }
            }
        }
    }
    
    private func monitorEnrollmentProgress() async {
        // 监控个人语音注册进度
        let maxWaitTime = 300 // 5分钟超时
        var elapsedTime = 0
        
        while isEnrolling && elapsedTime < maxWaitTime {
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒检查一次
            elapsedTime += 2
            
            await MainActor.run {
                // 更新进度显示
                enrollmentProgress = min(Float(elapsedTime) / Float(maxWaitTime), 0.9)
                
                // 检查是否有新的个人语音
                let currentVoices = getPersonalVoices()
                if !currentVoices.isEmpty {
                    // 发现个人语音，注册成功
                    isEnrolling = false
                    enrollmentProgress = 1.0
                    checkPersonalVoiceAvailability()
                    return
                }
            }
        }
        
        // 超时或用户取消
        await MainActor.run {
            if isEnrolling {
                isEnrolling = false
                enrollmentProgress = 0.0
                enrollmentError = "注册超时或被取消，请重试"
            }
        }
    }
    
    // MARK: - Error Handling
    var enrollmentError: String?
    
    func handleEnrollmentError(_ error: Error) {
        isEnrolling = false
        enrollmentProgress = 0.0
        enrollmentError = TTSError.personalVoiceEnrollmentFailed.localizedDescription
        print("Personal Voice enrollment error: \(error.localizedDescription)")
    }
    
    func clearEnrollmentError() {
        enrollmentError = nil
    }
    
    func cancelEnrollment() {
        isEnrolling = false
        enrollmentProgress = 0.0
        enrollmentError = nil
    }
    
    // MARK: - Helper Methods
    func getPersonalVoices() -> [AVSpeechSynthesisVoice] {
        return AVSpeechSynthesisVoice.speechVoices().filter { $0.voiceTraits.contains(.isPersonalVoice) }
    }
    
    var hasPersonalVoices: Bool {
        return !getPersonalVoices().isEmpty
    }
    
    var canEnrollPersonalVoice: Bool {
        return isPersonalVoiceSupported && personalVoiceAuthorizationStatus == .authorized && !isEnrolling
    }
}
